<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>环境管理要求-自行监测要求</h3>
    </div>
    <div class="detail-content">
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <tr>
              <th>污染源类别</th>
              <th>排放口编号</th>
              <th>排放口名称</th>
              <th>监测内容</th>
              <th>污染物名称</th>
              <th>监测设施</th>
              <th>自动监测是否联网</th>
              <th>自动监测仪器名称</th>
              <th>自动监测设施安装位置</th>
              <th>是否符合安装、运行、维护等管理要求</th>
              <th>手工监测采样方法及个数</th>
              <th>手工监测频次</th>
              <th>手工测定方法</th>
              <th>其他信息</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!data || data.length === 0">
              <td colspan="14" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in data" :key="item.id">
              <!--  污染源类别-->
              <td>{{ item.pollName || "-" }}</td>
              <!--  排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  监测内容-->
              <td>{{ item.monContent || "-" }}</td>
              <!--  污染物名称-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  监测设施-->
              <td>{{ item.monitorName || "-" }}</td>
              <!--  自动监测是否联网-->
              <td>{{ item.isLinksName || "-" }}</td>
              <!--  自动监测仪器名称-->
              <td>{{ item.instrument || "-" }}</td>
              <!--  自动监测设施安装位置-->
              <td>{{ item.position || "-" }}</td>
              <!--  是否符合安装、运行、维护等管理要求-->
              <td>{{ item.isSafe || "-" }}</td>
              <!--  手工监测采样方法及个数-->
              <td>{{ item.qtMonitorSampName || "-" }}</td>
              <!--  手工监测频次-->
              <td>{{ item.qtMonitorFreqName || "-" }}</td>
              <!--  手工测定方法-->
              <td>{{ item.testMethodName || "-" }}</td>
              <!--  其他信息-->
              <td>{{ item.otherContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SelfMonitorDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    data() {
      return this.raw.selfMonitorInfoList || [];
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }
  }
}
</style>
