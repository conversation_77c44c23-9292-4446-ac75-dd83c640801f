<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>在线监控数据-废气</h3>
    </div>
    <div class="detail-content">
      <el-card shadow="never">
        <div class="content-placeholder">
          <p>在线监控数据-废气详情内容</p>
          <p>{{ data }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { gasPageList } from "@/api/firmManage";

export default {
  name: "WasteGasDetail",
  props: {
    id: {
      type: String,
      default: ""
    },
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      queryParams: {
        stationId: this.id,
        startTime: "2020-01-01 00:00:00",
        endTime: "2025-12-31 00:00:00",
        keywords: null
      },
      data: {}
    };
  },
  created() {
    console.log(this.raw, "==============");
    this.handleQuery();
  },
  methods: {
    // 组件方法
    handleQuery() {
      this.loading = true;
      gasPageList(this.queryParams)
        .then(res => {
          console.log(res);
          this.data = res.data.data;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow-y: auto;

    .content-placeholder {
      padding: 40px;
      color: #999999;
      text-align: center;

      p {
        margin: 16px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
